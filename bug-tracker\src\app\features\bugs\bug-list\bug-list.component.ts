import { Component, OnInit, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { BugService } from '../../../core/services/bug.service';
import { ProjectService } from '../../../core/services/project.service';
import { AuthService } from '../../../core/services/auth.service';
import {
  Bug,
  BugSummary,
  BugStatus,
  BugSeverity,
  BugPriority,
  BugFilter,
  getBugStatusColor,
  getBugSeverityColor,
  getBugPriorityColor
} from '../../../core/models/bug.model';
import { Project } from '../../../core/models/project.model';

@Component({
  selector: 'app-bug-list',
  standalone: true,
  imports: [CommonModule, RouterModule, FormsModule],
  template: `
    <div class="bug-list">
      <!-- Header -->
      <div class="bug-list__header">
        <div class="bug-list__title-section">
          <h1 class="bug-list__title">Bug List</h1>
          <div class="bug-list__stats">
            <span class="stat-item">
              <span class="stat-value">{{ totalBugs }}</span>
              <span class="stat-label">Total</span>
            </span>
            <span class="stat-item">
              <span class="stat-value">{{ openBugs }}</span>
              <span class="stat-label">Open</span>
            </span>
            <span class="stat-item">
              <span class="stat-value">{{ criticalBugs }}</span>
              <span class="stat-label">Critical</span>
            </span>
          </div>
        </div>
        <div class="bug-list__actions">
          <a routerLink="/bugs/create" class="btn btn-primary">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 5v14M5 12h14"/>
            </svg>
            Report Bug
          </a>
        </div>
      </div>

      <!-- Filters -->
      <div class="bug-list__filters">
        <div class="filter-row">
          <div class="filter-group">
            <label class="filter-label">Search</label>
            <input
              type="text"
              class="filter-input"
              placeholder="Search bugs..."
              [(ngModel)]="searchTerm"
              (input)="onSearch()"
            />
          </div>

          <div class="filter-group">
            <label class="filter-label">Project</label>
            <select class="filter-select" [(ngModel)]="selectedProjectId" (change)="onFilterChange()">
              <option value="">All Projects</option>
              <option *ngFor="let project of projects" [value]="project.id">{{ project.name }}</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Status</label>
            <select class="filter-select" [(ngModel)]="selectedStatus" (change)="onFilterChange()">
              <option value="">All Statuses</option>
              <option *ngFor="let status of statusOptions" [value]="status">{{ status }}</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Severity</label>
            <select class="filter-select" [(ngModel)]="selectedSeverity" (change)="onFilterChange()">
              <option value="">All Severities</option>
              <option *ngFor="let severity of severityOptions" [value]="severity">{{ severity }}</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Priority</label>
            <select class="filter-select" [(ngModel)]="selectedPriority" (change)="onFilterChange()">
              <option value="">All Priorities</option>
              <option *ngFor="let priority of priorityOptions" [value]="priority">{{ priority }}</option>
            </select>
          </div>

          <div class="filter-group">
            <label class="filter-label">Assignment</label>
            <select class="filter-select" [(ngModel)]="selectedAssignment" (change)="onFilterChange()">
              <option value="">All Bugs</option>
              <option value="assigned-to-me">Assigned to Me</option>
              <option value="reported-by-me">Reported by Me</option>
              <option value="unassigned">Unassigned</option>
            </select>
          </div>

          <button class="btn btn-secondary" (click)="clearFilters()">Clear Filters</button>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="loading-container">
        <div class="loading-spinner"></div>
        <p>Loading bugs...</p>
      </div>

      <!-- Bug Table -->
      <div *ngIf="!loading" class="bug-list__table-container">
        <table class="bug-table">
          <thead>
            <tr>
              <th class="sortable" (click)="onSort('id')">
                ID
                <span class="sort-indicator" [class.active]="sortField === 'id'">
                  {{ sortDirection === 'asc' ? '↑' : '↓' }}
                </span>
              </th>
              <th class="sortable" (click)="onSort('title')">
                Title
                <span class="sort-indicator" [class.active]="sortField === 'title'">
                  {{ sortDirection === 'asc' ? '↑' : '↓' }}
                </span>
              </th>
              <th>Project</th>
              <th>Module</th>
              <th>Status</th>
              <th>Severity</th>
              <th>Priority</th>
              <th>Assigned To</th>
              <th class="sortable" (click)="onSort('issueDate')">
                Reported
                <span class="sort-indicator" [class.active]="sortField === 'issueDate'">
                  {{ sortDirection === 'asc' ? '↑' : '↓' }}
                </span>
              </th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let bug of paginatedBugs; trackBy: trackByBugId"
                class="bug-row"
                [class.overdue]="bug.isOverdue">
              <td class="bug-id">
                <a [routerLink]="['/bugs', bug.id]" class="bug-link">
                  #{{ bug.id.substring(0, 8) }}
                </a>
              </td>
              <td class="bug-title">
                <a [routerLink]="['/bugs', bug.id]" class="bug-link">
                  {{ bug.title }}
                </a>
              </td>
              <td class="bug-project">{{ bug.projectName }}</td>
              <td class="bug-module">{{ bug.moduleName }}</td>
              <td class="bug-status">
                <span class="status-badge" [attr.data-status]="getBugStatusColor(bug.status)">
                  {{ bug.status }}
                </span>
              </td>
              <td class="bug-severity">
                <span class="severity-badge" [attr.data-severity]="getBugSeverityColor(bug.severity)">
                  {{ bug.severity }}
                </span>
              </td>
              <td class="bug-priority">
                <span class="priority-badge" [attr.data-priority]="getBugPriorityColor(bug.priority)">
                  {{ bug.priority }}
                </span>
              </td>
              <td class="bug-assigned">
                <div class="assignee-info">
                  <div *ngIf="bug.devAssigned" class="assignee">
                    <span class="assignee-label">Dev:</span>
                    <span class="assignee-name">{{ bug.devAssigned }}</span>
                  </div>
                  <div *ngIf="bug.qaAssigned" class="assignee">
                    <span class="assignee-label">QA:</span>
                    <span class="assignee-name">{{ bug.qaAssigned }}</span>
                  </div>
                  <span *ngIf="!bug.devAssigned && !bug.qaAssigned" class="unassigned">Unassigned</span>
                </div>
              </td>
              <td class="bug-date">
                <span class="date-text">{{ formatDate(bug.issueDate) }}</span>
                <span class="days-ago">({{ bug.daysSinceReported }} days ago)</span>
              </td>
              <td class="bug-actions">
                <div class="action-buttons">
                  <a [routerLink]="['/bugs', bug.id]" class="btn btn-sm btn-outline" title="View Details">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                      <circle cx="12" cy="12" r="3"/>
                    </svg>
                  </a>
                  <a [routerLink]="['/bugs', bug.id, 'edit']" class="btn btn-sm btn-outline" title="Edit Bug">
                    <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                    </svg>
                  </a>
                </div>
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Empty State -->
        <div *ngIf="paginatedBugs.length === 0" class="empty-state">
          <div class="empty-state__icon">
            <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1">
              <circle cx="11" cy="11" r="8"/>
              <path d="M21 21l-4.35-4.35"/>
            </svg>
          </div>
          <h3>No bugs found</h3>
          <p>Try adjusting your filters or create a new bug report.</p>
          <a routerLink="/bugs/create" class="btn btn-primary">Report Bug</a>
        </div>
      </div>

      <!-- Pagination -->
      <div *ngIf="!loading && totalPages > 1" class="pagination">
        <button
          class="pagination-btn"
          [disabled]="currentPage === 1"
          (click)="goToPage(currentPage - 1)">
          Previous
        </button>

        <div class="pagination-pages">
          <button
            *ngFor="let page of getVisiblePages()"
            class="pagination-page"
            [class.active]="page === currentPage"
            (click)="goToPage(page)">
            {{ page }}
          </button>
        </div>

        <button
          class="pagination-btn"
          [disabled]="currentPage === totalPages"
          (click)="goToPage(currentPage + 1)">
          Next
        </button>

        <div class="pagination-info">
          Showing {{ (currentPage - 1) * pageSize + 1 }} to {{ Math.min(currentPage * pageSize, totalBugs) }} of {{ totalBugs }} bugs
        </div>
      </div>
    </div>
  `,
  styles: [`
    .bug-list {
      padding: var(--spacing-6);
      max-width: 100%;
      overflow-x: auto;
    }

    .bug-list__header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: var(--spacing-6);
      gap: var(--spacing-4);
    }

    .bug-list__title-section {
      flex: 1;
    }

    .bug-list__title {
      font-size: var(--font-size-3xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-gray-900);
      margin: 0 0 var(--spacing-3) 0;
    }

    .bug-list__stats {
      display: flex;
      gap: var(--spacing-4);
    }

    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: var(--spacing-2) var(--spacing-3);
      background: var(--color-gray-50);
      border-radius: var(--border-radius-md);
      min-width: 60px;
    }

    .stat-value {
      font-size: var(--font-size-xl);
      font-weight: var(--font-weight-bold);
      color: var(--color-primary-600);
    }

    .stat-label {
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
      margin-top: var(--spacing-1);
    }

    .bug-list__actions {
      display: flex;
      gap: var(--spacing-3);
    }

    .bug-list__filters {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      padding: var(--spacing-4);
      margin-bottom: var(--spacing-6);
    }

    .filter-row {
      display: flex;
      gap: var(--spacing-4);
      align-items: end;
      flex-wrap: wrap;
    }

    .filter-group {
      display: flex;
      flex-direction: column;
      min-width: 150px;
    }

    .filter-label {
      font-size: var(--font-size-sm);
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-700);
      margin-bottom: var(--spacing-1);
    }

    .filter-input,
    .filter-select {
      padding: var(--spacing-2) var(--spacing-3);
      border: 1px solid var(--color-gray-300);
      border-radius: var(--border-radius-md);
      font-size: var(--font-size-sm);
      background: var(--color-white);
      transition: border-color 0.2s ease;
    }

    .filter-input:focus,
    .filter-select:focus {
      outline: none;
      border-color: var(--color-primary-500);
      box-shadow: 0 0 0 3px var(--color-primary-100);
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-12);
      color: var(--color-gray-600);
    }

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid var(--color-gray-200);
      border-top: 3px solid var(--color-primary-500);
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: var(--spacing-3);
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .bug-list__table-container {
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
      overflow: hidden;
    }

    .bug-table {
      width: 100%;
      border-collapse: collapse;
    }

    .bug-table th {
      background: var(--color-gray-50);
      padding: var(--spacing-3) var(--spacing-4);
      text-align: left;
      font-weight: var(--font-weight-semibold);
      color: var(--color-gray-900);
      border-bottom: 1px solid var(--color-gray-200);
      font-size: var(--font-size-sm);
    }

    .bug-table th.sortable {
      cursor: pointer;
      user-select: none;
      position: relative;
    }

    .bug-table th.sortable:hover {
      background: var(--color-gray-100);
    }

    .sort-indicator {
      margin-left: var(--spacing-1);
      opacity: 0.3;
      font-size: var(--font-size-xs);
    }

    .sort-indicator.active {
      opacity: 1;
      color: var(--color-primary-600);
    }

    .bug-table td {
      padding: var(--spacing-3) var(--spacing-4);
      border-bottom: 1px solid var(--color-gray-100);
      font-size: var(--font-size-sm);
      vertical-align: top;
    }

    .bug-row:hover {
      background: var(--color-gray-25);
    }

    .bug-row.overdue {
      background: var(--color-red-25);
    }

    .bug-row.overdue:hover {
      background: var(--color-red-50);
    }

    .bug-link {
      color: var(--color-primary-600);
      text-decoration: none;
      font-weight: var(--font-weight-medium);
    }

    .bug-link:hover {
      color: var(--color-primary-700);
      text-decoration: underline;
    }

    .bug-id {
      font-family: var(--font-mono);
      font-size: var(--font-size-xs);
    }

    .bug-title {
      max-width: 300px;
      word-wrap: break-word;
    }

    .status-badge,
    .severity-badge,
    .priority-badge {
      display: inline-block;
      padding: var(--spacing-1) var(--spacing-2);
      border-radius: var(--border-radius-full);
      font-size: var(--font-size-xs);
      font-weight: var(--font-weight-medium);
      text-transform: uppercase;
      letter-spacing: 0.025em;
    }

    .status-badge[data-status="success"] { background: var(--color-green-100); color: var(--color-green-800); }
    .status-badge[data-status="warning"] { background: var(--color-yellow-100); color: var(--color-yellow-800); }
    .status-badge[data-status="error"] { background: var(--color-red-100); color: var(--color-red-800); }
    .status-badge[data-status="info"] { background: var(--color-blue-100); color: var(--color-blue-800); }
    .status-badge[data-status="gray"] { background: var(--color-gray-100); color: var(--color-gray-800); }

    .severity-badge[data-severity="success"] { background: var(--color-green-100); color: var(--color-green-800); }
    .severity-badge[data-severity="warning"] { background: var(--color-yellow-100); color: var(--color-yellow-800); }
    .severity-badge[data-severity="error"] { background: var(--color-red-100); color: var(--color-red-800); }
    .severity-badge[data-severity="gray"] { background: var(--color-gray-100); color: var(--color-gray-800); }

    .priority-badge[data-priority="success"] { background: var(--color-green-100); color: var(--color-green-800); }
    .priority-badge[data-priority="warning"] { background: var(--color-yellow-100); color: var(--color-yellow-800); }
    .priority-badge[data-priority="error"] { background: var(--color-red-100); color: var(--color-red-800); }
    .priority-badge[data-priority="gray"] { background: var(--color-gray-100); color: var(--color-gray-800); }

    .assignee-info {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-1);
    }

    .assignee {
      display: flex;
      gap: var(--spacing-1);
    }

    .assignee-label {
      font-weight: var(--font-weight-medium);
      color: var(--color-gray-600);
      font-size: var(--font-size-xs);
    }

    .assignee-name {
      color: var(--color-gray-900);
      font-size: var(--font-size-xs);
    }

    .unassigned {
      color: var(--color-gray-500);
      font-style: italic;
      font-size: var(--font-size-xs);
    }

    .bug-date {
      white-space: nowrap;
    }

    .date-text {
      display: block;
      color: var(--color-gray-900);
    }

    .days-ago {
      display: block;
      color: var(--color-gray-500);
      font-size: var(--font-size-xs);
      margin-top: var(--spacing-1);
    }

    .action-buttons {
      display: flex;
      gap: var(--spacing-1);
    }

    .empty-state {
      text-align: center;
      padding: var(--spacing-12);
      color: var(--color-gray-600);
    }

    .empty-state__icon {
      margin-bottom: var(--spacing-4);
      color: var(--color-gray-400);
    }

    .empty-state h3 {
      margin: 0 0 var(--spacing-2) 0;
      color: var(--color-gray-900);
    }

    .empty-state p {
      margin: 0 0 var(--spacing-4) 0;
    }

    .pagination {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: var(--spacing-6);
      padding: var(--spacing-4);
      background: var(--color-white);
      border: 1px solid var(--color-gray-200);
      border-radius: var(--border-radius-lg);
    }

    .pagination-pages {
      display: flex;
      gap: var(--spacing-1);
    }

    .pagination-btn,
    .pagination-page {
      padding: var(--spacing-2) var(--spacing-3);
      border: 1px solid var(--color-gray-300);
      background: var(--color-white);
      color: var(--color-gray-700);
      border-radius: var(--border-radius-md);
      cursor: pointer;
      font-size: var(--font-size-sm);
      transition: all 0.2s ease;
    }

    .pagination-btn:hover:not(:disabled),
    .pagination-page:hover {
      background: var(--color-gray-50);
      border-color: var(--color-gray-400);
    }

    .pagination-page.active {
      background: var(--color-primary-500);
      border-color: var(--color-primary-500);
      color: var(--color-white);
    }

    .pagination-btn:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    .pagination-info {
      font-size: var(--font-size-sm);
      color: var(--color-gray-600);
    }

    @media (max-width: 768px) {
      .bug-list {
        padding: var(--spacing-4);
      }

      .bug-list__header {
        flex-direction: column;
        align-items: stretch;
      }

      .filter-row {
        flex-direction: column;
      }

      .filter-group {
        min-width: auto;
      }

      .bug-table {
        font-size: var(--font-size-xs);
      }

      .bug-table th,
      .bug-table td {
        padding: var(--spacing-2);
      }

      .pagination {
        flex-direction: column;
        gap: var(--spacing-3);
      }
    }
  `]
})
export class BugListComponent implements OnInit {
  private bugService = inject(BugService);
  private projectService = inject(ProjectService);
  private authService = inject(AuthService);

  // Data
  bugs: BugSummary[] = [];
  paginatedBugs: BugSummary[] = [];
  projects: Project[] = [];

  // Filter state
  searchTerm = '';
  selectedProjectId = '';
  selectedStatus = '';
  selectedSeverity = '';
  selectedPriority = '';
  selectedAssignment = '';

  // Sorting state
  sortField = 'issueDate';
  sortDirection: 'asc' | 'desc' = 'desc';

  // Pagination state
  currentPage = 1;
  pageSize = 20;
  totalBugs = 0;
  totalPages = 0;

  // UI state
  loading = false;

  // Statistics
  openBugs = 0;
  criticalBugs = 0;

  // Options for filters
  statusOptions = Object.values(BugStatus);
  severityOptions = Object.values(BugSeverity);
  priorityOptions = Object.values(BugPriority);

  // Expose Math for template
  Math = Math;

  ngOnInit() {
    this.loadProjects();
    this.loadBugs();
  }

  loadProjects() {
    this.projects = this.projectService.getAllProjects();
  }

  loadBugs() {
    this.loading = true;

    const filter = this.buildFilter();

    this.bugService.getBugSummaries(filter).subscribe({
      next: (bugs) => {
        this.bugs = this.sortBugs(bugs);
        this.totalBugs = this.bugs.length;
        this.updateStatistics();
        this.updatePagination();
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading bugs:', error);
        this.loading = false;
      }
    });
  }

  private buildFilter(): BugFilter {
    const currentUser = this.authService.getCurrentUser();
    const filter: BugFilter = {};

    if (this.searchTerm) {
      filter.search = this.searchTerm;
    }

    if (this.selectedProjectId) {
      filter.projectId = this.selectedProjectId;
    }

    if (this.selectedStatus) {
      filter.status = [this.selectedStatus as BugStatus];
    }

    if (this.selectedSeverity) {
      filter.severity = [this.selectedSeverity as BugSeverity];
    }

    if (this.selectedPriority) {
      filter.priority = [this.selectedPriority as BugPriority];
    }

    if (this.selectedAssignment && currentUser) {
      switch (this.selectedAssignment) {
        case 'assigned-to-me':
          filter.assignedToId = currentUser.id;
          break;
        case 'reported-by-me':
          filter.reportedById = currentUser.id;
          break;
        case 'unassigned':
          // This would need special handling in the service
          break;
      }
    }

    return filter;
  }

  private sortBugs(bugs: BugSummary[]): BugSummary[] {
    return bugs.sort((a, b) => {
      let aValue: any;
      let bValue: any;

      switch (this.sortField) {
        case 'id':
          aValue = a.id;
          bValue = b.id;
          break;
        case 'title':
          aValue = a.title.toLowerCase();
          bValue = b.title.toLowerCase();
          break;
        case 'issueDate':
          aValue = new Date(a.issueDate).getTime();
          bValue = new Date(b.issueDate).getTime();
          break;
        default:
          aValue = a.issueDate;
          bValue = b.issueDate;
      }

      if (aValue < bValue) {
        return this.sortDirection === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return this.sortDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });
  }

  private updateStatistics() {
    this.openBugs = this.bugs.filter(bug => bug.status !== BugStatus.CLOSED).length;
    this.criticalBugs = this.bugs.filter(bug => bug.severity === BugSeverity.CRITICAL).length;
  }

  private updatePagination() {
    this.totalPages = Math.ceil(this.totalBugs / this.pageSize);
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = startIndex + this.pageSize;
    this.paginatedBugs = this.bugs.slice(startIndex, endIndex);
  }

  // Event handlers
  onSearch() {
    this.currentPage = 1;
    this.loadBugs();
  }

  onFilterChange() {
    this.currentPage = 1;
    this.loadBugs();
  }

  onSort(field: string) {
    if (this.sortField === field) {
      this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
      this.sortField = field;
      this.sortDirection = 'asc';
    }
    this.bugs = this.sortBugs(this.bugs);
    this.updatePagination();
  }

  clearFilters() {
    this.searchTerm = '';
    this.selectedProjectId = '';
    this.selectedStatus = '';
    this.selectedSeverity = '';
    this.selectedPriority = '';
    this.selectedAssignment = '';
    this.currentPage = 1;
    this.loadBugs();
  }

  goToPage(page: number) {
    if (page >= 1 && page <= this.totalPages) {
      this.currentPage = page;
      this.updatePagination();
    }
  }

  getVisiblePages(): number[] {
    const pages: number[] = [];
    const maxVisible = 5;
    const half = Math.floor(maxVisible / 2);

    let start = Math.max(1, this.currentPage - half);
    let end = Math.min(this.totalPages, start + maxVisible - 1);

    if (end - start + 1 < maxVisible) {
      start = Math.max(1, end - maxVisible + 1);
    }

    for (let i = start; i <= end; i++) {
      pages.push(i);
    }

    return pages;
  }

  // Utility methods
  trackByBugId(index: number, bug: BugSummary): string {
    return bug.id;
  }

  getBugStatusColor(status: BugStatus): string {
    return getBugStatusColor(status);
  }

  getBugSeverityColor(severity: BugSeverity): string {
    return getBugSeverityColor(severity);
  }

  getBugPriorityColor(priority: BugPriority): string {
    return getBugPriorityColor(priority);
  }

  formatDate(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    }).format(new Date(date));
  }
}
