import { Injectable, inject } from '@angular/core';
import { Observable, BehaviorSubject, of, throwError } from 'rxjs';
import { delay, map } from 'rxjs/operators';
import { 
  Project, 
  ProjectSummary, 
  CreateProjectRequest, 
  UpdateProjectRequest,
  ProjectStatus,
  ProjectListResponse,
  ProjectFilter,
  ProjectMetrics
} from '../models/project.model';
import { ApiResponse, PaginatedResponse, generateId } from '../models/common.model';
import { UserService } from './user.service';

@Injectable({
  providedIn: 'root'
})
export class ProjectService {
  private userService = inject(UserService);
  private projectsSubject = new BehaviorSubject<Project[]>([]);
  private storageKey = 'bugtracker_projects';

  projects$ = this.projectsSubject.asObservable();

  constructor() {
    this.loadProjectsFromStorage();
  }

  // CRUD Operations
  getProjects(filter?: ProjectFilter, page = 1, pageSize = 20): Observable<ProjectListResponse> {
    return this.projects$.pipe(
      map(projects => {
        let filteredProjects = [...projects];

        // Apply filters
        if (filter) {
          if (filter.status && filter.status.length > 0) {
            filteredProjects = filteredProjects.filter(p => filter.status!.includes(p.status));
          }
          
          if (filter.search) {
            const searchLower = filter.search.toLowerCase();
            filteredProjects = filteredProjects.filter(p => 
              p.name.toLowerCase().includes(searchLower) ||
              p.description.toLowerCase().includes(searchLower)
            );
          }

          if (filter.isActive !== undefined) {
            filteredProjects = filteredProjects.filter(p => p.isActive === filter.isActive);
          }

          if (filter.createdDateRange) {
            filteredProjects = filteredProjects.filter(p => {
              const createdDate = new Date(p.createdAt);
              return createdDate >= filter.createdDateRange!.start && 
                     createdDate <= filter.createdDateRange!.end;
            });
          }
        }

        // Sort by creation date (newest first)
        filteredProjects.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());

        // Pagination
        const total = filteredProjects.length;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        const paginatedProjects = filteredProjects.slice(startIndex, endIndex);

        // Convert to ProjectSummary
        const projectSummaries: ProjectSummary[] = paginatedProjects.map(this.toProjectSummary);

        return {
          projects: projectSummaries,
          total,
          page,
          pageSize,
          totalPages: Math.ceil(total / pageSize)
        };
      }),
      delay(300) // Simulate API delay
    );
  }

  getProjectById(id: string): Observable<Project | null> {
    return this.projects$.pipe(
      map(projects => projects.find(p => p.id === id) || null),
      delay(200)
    );
  }

  createProject(request: CreateProjectRequest): Observable<ApiResponse<Project>> {
    const currentUser = this.userService.getCurrentUser();
    if (!currentUser) {
      return throwError(() => new Error('User not authenticated'));
    }

    const newProject: Project = {
      id: generateId(),
      name: request.name,
      description: request.description,
      projectUrl: request.projectUrl,
      modules: request.modules.map(moduleReq => ({
        id: generateId(),
        name: moduleReq.name,
        description: moduleReq.description,
        features: moduleReq.features.map(featureReq => ({
          id: generateId(),
          name: featureReq.name,
          description: featureReq.description,
          moduleId: '', // Will be set after module creation
          createdAt: new Date(),
          updatedAt: new Date()
        })),
        projectId: '', // Will be set after project creation
        createdAt: new Date(),
        updatedAt: new Date()
      })),
      teamMembers: request.teamMembers.map(memberReq => ({
        id: generateId(),
        userId: memberReq.userId,
        user: this.userService.getUserById(memberReq.userId)!,
        projectId: '', // Will be set after project creation
        role: memberReq.role,
        assignedAt: new Date(),
        assignedBy: currentUser.id
      })),
      status: ProjectStatus.PLANNING,
      createdBy: currentUser.id,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true
    };

    // Set references
    newProject.modules.forEach(module => {
      module.projectId = newProject.id;
      module.features.forEach(feature => {
        feature.moduleId = module.id;
      });
    });

    newProject.teamMembers.forEach(member => {
      member.projectId = newProject.id;
    });

    return of(null).pipe(
      delay(500),
      map(() => {
        const currentProjects = this.projectsSubject.value;
        const updatedProjects = [...currentProjects, newProject];
        this.projectsSubject.next(updatedProjects);
        this.saveProjectsToStorage(updatedProjects);

        return {
          success: true,
          data: newProject,
          message: 'Project created successfully',
          timestamp: new Date()
        };
      })
    );
  }

  updateProject(id: string, request: UpdateProjectRequest): Observable<ApiResponse<Project>> {
    return this.projects$.pipe(
      delay(300),
      map(projects => {
        const projectIndex = projects.findIndex(p => p.id === id);
        if (projectIndex === -1) {
          throw new Error('Project not found');
        }

        const updatedProject = {
          ...projects[projectIndex],
          ...request,
          updatedAt: new Date()
        };

        const updatedProjects = [...projects];
        updatedProjects[projectIndex] = updatedProject;
        
        this.projectsSubject.next(updatedProjects);
        this.saveProjectsToStorage(updatedProjects);

        return {
          success: true,
          data: updatedProject,
          message: 'Project updated successfully',
          timestamp: new Date()
        };
      })
    );
  }

  deleteProject(id: string): Observable<ApiResponse<void>> {
    return this.projects$.pipe(
      delay(300),
      map(projects => {
        const projectExists = projects.some(p => p.id === id);
        if (!projectExists) {
          throw new Error('Project not found');
        }

        const updatedProjects = projects.filter(p => p.id !== id);
        this.projectsSubject.next(updatedProjects);
        this.saveProjectsToStorage(updatedProjects);

        return {
          success: true,
          message: 'Project deleted successfully',
          timestamp: new Date()
        };
      })
    );
  }

  getProjectMetrics(projectId: string): Observable<ProjectMetrics> {
    // Mock metrics - in real app, this would come from backend
    return of({
      projectId,
      totalBugsRaised: 45,
      totalBugsAssigned: 42,
      totalBugsFixed: 38,
      totalBugsPending: 7,
      fixRate: 90.5,
      reopenRate: 5.3,
      averageTimeToFix: 18.5,
      averageTimeToRetest: 4.2,
      bugsByPriority: {
        low: 12,
        medium: 18,
        high: 12,
        critical: 3
      },
      bugsBySeverity: {
        low: 15,
        medium: 20,
        high: 8,
        critical: 2
      },
      bugsOverTime: [
        { date: new Date('2024-01-01'), opened: 5, closed: 2, total: 3 },
        { date: new Date('2024-01-02'), opened: 3, closed: 4, total: 2 },
        { date: new Date('2024-01-03'), opened: 7, closed: 3, total: 6 }
      ]
    }).pipe(delay(400));
  }

  getUserProjects(userId: string): Observable<ProjectSummary[]> {
    return this.projects$.pipe(
      map(projects => {
        const userProjects = projects.filter(project => 
          project.teamMembers.some(member => member.userId === userId) ||
          project.createdBy === userId
        );
        return userProjects.map(this.toProjectSummary);
      }),
      delay(200)
    );
  }

  // Helper methods
  private toProjectSummary(project: Project): ProjectSummary {
    return {
      id: project.id,
      name: project.name,
      description: project.description,
      status: project.status,
      totalModules: project.modules.length,
      totalFeatures: project.modules.reduce((sum, module) => sum + module.features.length, 0),
      totalTeamMembers: project.teamMembers.length,
      totalBugs: 0, // Would be calculated from bug service
      activeBugs: 0,
      resolvedBugs: 0,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt
    };
  }

  private loadProjectsFromStorage(): void {
    if (typeof window === 'undefined' || !window.localStorage) {
      this.initializeMockData();
      return;
    }

    try {
      const stored = localStorage.getItem(this.storageKey);
      if (stored) {
        const projects = JSON.parse(stored);
        // Convert date strings back to Date objects
        projects.forEach((project: any) => {
          project.createdAt = new Date(project.createdAt);
          project.updatedAt = new Date(project.updatedAt);
          project.modules.forEach((module: any) => {
            module.createdAt = new Date(module.createdAt);
            module.updatedAt = new Date(module.updatedAt);
            module.features.forEach((feature: any) => {
              feature.createdAt = new Date(feature.createdAt);
              feature.updatedAt = new Date(feature.updatedAt);
            });
          });
          project.teamMembers.forEach((member: any) => {
            member.assignedAt = new Date(member.assignedAt);
          });
        });
        this.projectsSubject.next(projects);
      } else {
        // Initialize with mock data
        this.initializeMockData();
      }
    } catch (error) {
      console.error('Error loading projects from storage:', error);
      this.initializeMockData();
    }
  }

  private saveProjectsToStorage(projects: Project[]): void {
    if (typeof window === 'undefined' || !window.localStorage) {
      return;
    }

    try {
      localStorage.setItem(this.storageKey, JSON.stringify(projects));
    } catch (error) {
      console.error('Error saving projects to storage:', error);
    }
  }

  private initializeMockData(): void {
    const mockUsers = this.userService.getAllUsers();

    const mockProjects: Project[] = [
      {
        id: 'proj-1',
        name: 'E-Commerce Platform',
        description: 'A comprehensive online shopping platform with advanced features for both customers and administrators.',
        projectUrl: 'https://ecommerce.example.com',
        modules: [
          {
            id: 'mod-1',
            name: 'User Management',
            description: 'Handle user registration, authentication, and profiles',
            features: [
              { id: 'feat-1', name: 'User Registration', description: 'Allow new users to create accounts', moduleId: 'mod-1', createdAt: new Date(), updatedAt: new Date() },
              { id: 'feat-2', name: 'User Login', description: 'Authenticate existing users', moduleId: 'mod-1', createdAt: new Date(), updatedAt: new Date() },
              { id: 'feat-3', name: 'Profile Management', description: 'Users can update their profiles', moduleId: 'mod-1', createdAt: new Date(), updatedAt: new Date() }
            ],
            projectId: 'proj-1',
            createdAt: new Date(),
            updatedAt: new Date()
          },
          {
            id: 'mod-2',
            name: 'Product Catalog',
            description: 'Manage products, categories, and inventory',
            features: [
              { id: 'feat-4', name: 'Product Listing', description: 'Display products with filters', moduleId: 'mod-2', createdAt: new Date(), updatedAt: new Date() },
              { id: 'feat-5', name: 'Product Search', description: 'Search functionality for products', moduleId: 'mod-2', createdAt: new Date(), updatedAt: new Date() }
            ],
            projectId: 'proj-1',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ],
        teamMembers: [
          { id: 'tm-1', userId: 'user-1', user: mockUsers[0], projectId: 'proj-1', role: mockUsers[0].role, assignedAt: new Date(), assignedBy: 'user-1' },
          { id: 'tm-2', userId: 'user-2', user: mockUsers[1], projectId: 'proj-1', role: mockUsers[1].role, assignedAt: new Date(), assignedBy: 'user-1' },
          { id: 'tm-3', userId: 'user-3', user: mockUsers[2], projectId: 'proj-1', role: mockUsers[2].role, assignedAt: new Date(), assignedBy: 'user-1' }
        ],
        status: ProjectStatus.ACTIVE,
        createdBy: 'user-1',
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date(),
        isActive: true
      },
      {
        id: 'proj-2',
        name: 'Task Management System',
        description: 'A project management tool for teams to collaborate and track progress on various tasks and projects.',
        projectUrl: 'https://taskmanager.example.com',
        modules: [
          {
            id: 'mod-3',
            name: 'Task Management',
            description: 'Create, assign, and track tasks',
            features: [
              { id: 'feat-6', name: 'Create Tasks', description: 'Allow users to create new tasks', moduleId: 'mod-3', createdAt: new Date(), updatedAt: new Date() },
              { id: 'feat-7', name: 'Task Assignment', description: 'Assign tasks to team members', moduleId: 'mod-3', createdAt: new Date(), updatedAt: new Date() }
            ],
            projectId: 'proj-2',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ],
        teamMembers: [
          { id: 'tm-4', userId: 'user-1', user: mockUsers[0], projectId: 'proj-2', role: mockUsers[0].role, assignedAt: new Date(), assignedBy: 'user-1' },
          { id: 'tm-5', userId: 'user-4', user: mockUsers[3], projectId: 'proj-2', role: mockUsers[3].role, assignedAt: new Date(), assignedBy: 'user-1' }
        ],
        status: ProjectStatus.PLANNING,
        createdBy: 'user-1',
        createdAt: new Date('2024-02-01'),
        updatedAt: new Date(),
        isActive: true
      },
      {
        id: 'proj-3',
        name: 'Mobile Banking App',
        description: 'Secure mobile banking application with comprehensive financial services and real-time transaction processing.',
        projectUrl: 'https://mobilebank.example.com',
        modules: [
          {
            id: 'mod-4',
            name: 'Account Management',
            description: 'Manage bank accounts and balances',
            features: [
              { id: 'feat-8', name: 'Account Overview', description: 'Display account balances and details', moduleId: 'mod-4', createdAt: new Date(), updatedAt: new Date() },
              { id: 'feat-9', name: 'Transaction History', description: 'Show transaction history', moduleId: 'mod-4', createdAt: new Date(), updatedAt: new Date() },
              { id: 'feat-10', name: 'Fund Transfer', description: 'Transfer money between accounts', moduleId: 'mod-4', createdAt: new Date(), updatedAt: new Date() }
            ],
            projectId: 'proj-3',
            createdAt: new Date(),
            updatedAt: new Date()
          }
        ],
        teamMembers: [
          { id: 'tm-6', userId: 'user-2', user: mockUsers[1], projectId: 'proj-3', role: mockUsers[1].role, assignedAt: new Date(), assignedBy: 'user-1' },
          { id: 'tm-7', userId: 'user-3', user: mockUsers[2], projectId: 'proj-3', role: mockUsers[2].role, assignedAt: new Date(), assignedBy: 'user-1' },
          { id: 'tm-8', userId: 'user-4', user: mockUsers[3], projectId: 'proj-3', role: mockUsers[3].role, assignedAt: new Date(), assignedBy: 'user-1' },
          { id: 'tm-9', userId: 'user-5', user: mockUsers[4], projectId: 'proj-3', role: mockUsers[4].role, assignedAt: new Date(), assignedBy: 'user-1' }
        ],
        status: ProjectStatus.COMPLETED,
        createdBy: 'user-1',
        createdAt: new Date('2023-11-10'),
        updatedAt: new Date(),
        isActive: true
      }
    ];

    this.projectsSubject.next(mockProjects);
    this.saveProjectsToStorage(mockProjects);
  }
}
